class LocalUserEntity {
  final String id;
  final String email;
  final String phone;
  final String firstName;
  final String lastName;
  final String username;
  final String avatarUrl;
  final String tenantCode;
  final String partyTypeKey;
  final bool emailVerified;
  final bool phoneVerified;
  final String accessToken;
  final String refreshToken;
  final String tokenType;
  final int expiresIn;
  final int expiresAt;
  final String role;
  final String aud;
  final String emailConfirmedAt;
  final String confirmedAt;
  final String lastSignInAt;
  final String createdAt;
  final String updatedAt;
  final bool isAnonymous;
  final DateTime localCreatedAt;
  final DateTime localUpdatedAt;

  const LocalUserEntity({
    required this.id,
    required this.email,
    required this.phone,
    required this.firstName,
    required this.lastName,
    required this.username,
    required this.avatarUrl,
    required this.tenantCode,
    required this.partyTypeKey,
    required this.emailVerified,
    required this.phoneVerified,
    required this.accessToken,
    required this.refreshToken,
    required this.tokenType,
    required this.expiresIn,
    required this.expiresAt,
    required this.role,
    required this.aud,
    required this.emailConfirmedAt,
    required this.confirmedAt,
    required this.lastSignInAt,
    required this.createdAt,
    required this.updatedAt,
    required this.isAnonymous,
    required this.localCreatedAt,
    required this.localUpdatedAt,
  });

  // Convert from LoginModel to LocalUserEntity
  factory LocalUserEntity.fromLoginModel(
    dynamic loginModel, {
    DateTime? localCreatedAt,
    DateTime? localUpdatedAt,
  }) {
    final now = DateTime.now();
    final user = loginModel.user;
    final userMetadata = user.userMetadata;

    return LocalUserEntity(
      id: user.id,
      email: user.email,
      phone: user.phone,
      firstName: userMetadata.firstName,
      lastName: userMetadata.lastName,
      username: userMetadata.username,
      avatarUrl: userMetadata.avatarUrl,
      tenantCode: userMetadata.tenantCode,
      partyTypeKey: userMetadata.partyTypeKey,
      emailVerified: userMetadata.emailVerified,
      phoneVerified: userMetadata.phoneVerified,
      accessToken: loginModel.accessToken,
      refreshToken: loginModel.refreshToken,
      tokenType: loginModel.tokenType,
      expiresIn: loginModel.expiresIn,
      expiresAt: loginModel.expiresAt,
      role: user.role,
      aud: user.aud,
      emailConfirmedAt: user.emailConfirmedAt,
      confirmedAt: user.confirmedAt,
      lastSignInAt: user.lastSignInAt,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
      isAnonymous: user.isAnonymous,
      localCreatedAt: localCreatedAt ?? now,
      localUpdatedAt: localUpdatedAt ?? now,
    );
  }

  // Convert from SignupModel to LocalUserEntity
  factory LocalUserEntity.fromSignupModel(
    dynamic signupModel, {
    DateTime? localCreatedAt,
    DateTime? localUpdatedAt,
  }) {
    final now = DateTime.now();
    final user = signupModel.user;
    final userMetadata = user.userMetadata;

    return LocalUserEntity(
      id: user.id,
      email: user.email,
      phone: user.phone,
      // SignupModel has limited userMetadata, so we use defaults or extract from other fields
      firstName: '', // Not available in signup response
      lastName: '', // Not available in signup response
      username: '', // Not available in signup response
      avatarUrl: '', // Not available in signup response
      tenantCode: '', // Not available in signup response
      partyTypeKey: '', // Not available in signup response
      emailVerified: userMetadata.emailVerified,
      phoneVerified: userMetadata.phoneVerified,
      accessToken: signupModel.accessToken,
      refreshToken: signupModel.refreshToken,
      tokenType: signupModel.tokenType,
      expiresIn: signupModel.expiresIn,
      expiresAt: signupModel.expiresAt,
      role: user.role,
      aud: user.aud,
      emailConfirmedAt: user.emailConfirmedAt,
      confirmedAt: '', // Not available in signup model
      lastSignInAt: user.lastSignInAt,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
      isAnonymous: user.isAnonymous,
      localCreatedAt: localCreatedAt ?? now,
      localUpdatedAt: localUpdatedAt ?? now,
    );
  }

  // Convert to Map for SQLite
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'email': email,
      'phone': phone,
      'first_name': firstName,
      'last_name': lastName,
      'username': username,
      'avatar_url': avatarUrl,
      'tenant_code': tenantCode,
      'party_type_key': partyTypeKey,
      'email_verified': emailVerified ? 1 : 0,
      'phone_verified': phoneVerified ? 1 : 0,
      'access_token': accessToken,
      'refresh_token': refreshToken,
      'token_type': tokenType,
      'expires_in': expiresIn,
      'expires_at': expiresAt,
      'role': role,
      'aud': aud,
      'email_confirmed_at': emailConfirmedAt,
      'confirmed_at': confirmedAt,
      'last_sign_in_at': lastSignInAt,
      'created_at': createdAt,
      'updated_at': updatedAt,
      'is_anonymous': isAnonymous ? 1 : 0,
      'local_created_at': localCreatedAt.toIso8601String(),
      'local_updated_at': localUpdatedAt.toIso8601String(),
    };
  }

  // Convert from Map (SQLite result)
  factory LocalUserEntity.fromMap(Map<String, dynamic> map) {
    return LocalUserEntity(
      id: map['id'] as String,
      email: map['email'] as String,
      phone: map['phone'] as String,
      firstName: map['first_name'] as String,
      lastName: map['last_name'] as String,
      username: map['username'] as String,
      avatarUrl: map['avatar_url'] as String,
      tenantCode: map['tenant_code'] as String,
      partyTypeKey: map['party_type_key'] as String,
      emailVerified: (map['email_verified'] as int) == 1,
      phoneVerified: (map['phone_verified'] as int) == 1,
      accessToken: map['access_token'] as String,
      refreshToken: map['refresh_token'] as String,
      tokenType: map['token_type'] as String,
      expiresIn: map['expires_in'] as int,
      expiresAt: map['expires_at'] as int,
      role: map['role'] as String,
      aud: map['aud'] as String,
      emailConfirmedAt: map['email_confirmed_at'] as String,
      confirmedAt: map['confirmed_at'] as String,
      lastSignInAt: map['last_sign_in_at'] as String,
      createdAt: map['created_at'] as String,
      updatedAt: map['updated_at'] as String,
      isAnonymous: (map['is_anonymous'] as int) == 1,
      localCreatedAt: DateTime.parse(map['local_created_at'] as String),
      localUpdatedAt: DateTime.parse(map['local_updated_at'] as String),
    );
  }

  // Copy with method for updates
  LocalUserEntity copyWith({
    String? id,
    String? email,
    String? phone,
    String? firstName,
    String? lastName,
    String? username,
    String? avatarUrl,
    String? tenantCode,
    String? partyTypeKey,
    bool? emailVerified,
    bool? phoneVerified,
    String? accessToken,
    String? refreshToken,
    String? tokenType,
    int? expiresIn,
    int? expiresAt,
    String? role,
    String? aud,
    String? emailConfirmedAt,
    String? confirmedAt,
    String? lastSignInAt,
    String? createdAt,
    String? updatedAt,
    bool? isAnonymous,
    DateTime? localCreatedAt,
    DateTime? localUpdatedAt,
  }) {
    return LocalUserEntity(
      id: id ?? this.id,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      username: username ?? this.username,
      avatarUrl: avatarUrl ?? this.avatarUrl,
      tenantCode: tenantCode ?? this.tenantCode,
      partyTypeKey: partyTypeKey ?? this.partyTypeKey,
      emailVerified: emailVerified ?? this.emailVerified,
      phoneVerified: phoneVerified ?? this.phoneVerified,
      accessToken: accessToken ?? this.accessToken,
      refreshToken: refreshToken ?? this.refreshToken,
      tokenType: tokenType ?? this.tokenType,
      expiresIn: expiresIn ?? this.expiresIn,
      expiresAt: expiresAt ?? this.expiresAt,
      role: role ?? this.role,
      aud: aud ?? this.aud,
      emailConfirmedAt: emailConfirmedAt ?? this.emailConfirmedAt,
      confirmedAt: confirmedAt ?? this.confirmedAt,
      lastSignInAt: lastSignInAt ?? this.lastSignInAt,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isAnonymous: isAnonymous ?? this.isAnonymous,
      localCreatedAt: localCreatedAt ?? this.localCreatedAt,
      localUpdatedAt: localUpdatedAt ?? this.localUpdatedAt,
    );
  }

  @override
  String toString() {
    return 'LocalUserEntity(id: $id, email: $email, username: $username, firstName: $firstName, lastName: $lastName)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is LocalUserEntity && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
