import 'package:freezed_annotation/freezed_annotation.dart';

part 'signup_model.freezed.dart';
part 'signup_model.g.dart';

@freezed
class SignupModel with _$SignupModel {
  const factory SignupModel({
    @JsonKey(name: "access_token") required String accessToken,
    @<PERSON><PERSON><PERSON><PERSON>(name: "token_type") required String tokenType,
    @<PERSON>son<PERSON><PERSON>(name: "expires_in") required int expiresIn,
    @<PERSON>son<PERSON><PERSON>(name: "expires_at") required int expiresAt,
    @<PERSON>son<PERSON><PERSON>(name: "refresh_token") required String refreshToken,
    @<PERSON><PERSON><PERSON><PERSON>(name: "user") required User user,
  }) = _SignupModel;
}

@freezed
class User with _$User {
  const factory User({
    @J<PERSON><PERSON><PERSON>(name: "id") required String id,
    @J<PERSON><PERSON><PERSON>(name: "aud") required String aud,
    @<PERSON><PERSON><PERSON><PERSON>(name: "role") required String role,
    @<PERSON>son<PERSON><PERSON>(name: "email") required String email,
    @<PERSON><PERSON><PERSON><PERSON>(name: "email_confirmed_at") required String emailConfirmedAt,
    @<PERSON><PERSON><PERSON><PERSON>(name: "phone") required String phone,
    @<PERSON><PERSON><PERSON><PERSON>(name: "last_sign_in_at") required String lastSignInAt,
    @<PERSON><PERSON><PERSON><PERSON>(name: "app_metadata") required AppMetadata appMetadata,
    @Json<PERSON>ey(name: "user_metadata") required Data userMetadata,
    @JsonKey(name: "identities") required List<Identity> identities,
    @JsonKey(name: "created_at") required String createdAt,
    @JsonKey(name: "updated_at") required String updatedAt,
    @JsonKey(name: "is_anonymous") required bool isAnonymous,
  }) = _User;
}

@freezed
class AppMetadata with _$AppMetadata {
  const factory AppMetadata({
    @JsonKey(name: "provider") required String provider,
    @JsonKey(name: "providers") required List<String> providers,
  }) = _AppMetadata;
}

@freezed
class Identity with _$Identity {
  const factory Identity({
    @JsonKey(name: "identity_id") required String identityId,
    @JsonKey(name: "id") required String id,
    @JsonKey(name: "user_id") required String userId,
    @JsonKey(name: "identity_data") required Data identityData,
    @JsonKey(name: "provider") required String provider,
    @JsonKey(name: "last_sign_in_at") required String lastSignInAt,
    @JsonKey(name: "created_at") required String createdAt,
    @JsonKey(name: "updated_at") required String updatedAt,
    @JsonKey(name: "email") required String email,
  }) = _Identity;
}

@freezed
class Data with _$Data {
  const factory Data({
    @JsonKey(name: "address") required String address,
    @JsonKey(name: "avatar_url") required String avatarUrl,
    @JsonKey(name: "email") required String email,
    @JsonKey(name: "email_verified") required bool emailVerified,
    @JsonKey(name: "first_name") required String firstName,
    @JsonKey(name: "last_name") required String lastName,
    @JsonKey(name: "party_id") required String partyId,
    @JsonKey(name: "party_type_key") required String partyTypeKey,
    @JsonKey(name: "phone") required String phone,
    @JsonKey(name: "phone_verified") required bool phoneVerified,
    @JsonKey(name: "sub") required String sub,
    @JsonKey(name: "tenant_code") required String tenantCode,
  }) = _Data;
}
