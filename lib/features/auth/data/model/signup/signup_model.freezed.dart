// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'signup_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

SignupModel _$SignupModelFromJson(Map<String, dynamic> json) {
  return _SignupModel.fromJson(json);
}

/// @nodoc
mixin _$SignupModel {
  @JsonKey(name: "access_token")
  String get accessToken => throw _privateConstructorUsedError;
  @JsonKey(name: "token_type")
  String get tokenType => throw _privateConstructorUsedError;
  @JsonKey(name: "expires_in")
  int get expiresIn => throw _privateConstructorUsedError;
  @JsonKey(name: "expires_at")
  int get expiresAt => throw _privateConstructorUsedError;
  @JsonKey(name: "refresh_token")
  String get refreshToken => throw _privateConstructorUsedError;
  @JsonKey(name: "user")
  User get user => throw _privateConstructorUsedError;

  /// Serializes this SignupModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of SignupModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $SignupModelCopyWith<SignupModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SignupModelCopyWith<$Res> {
  factory $SignupModelCopyWith(
    SignupModel value,
    $Res Function(SignupModel) then,
  ) = _$SignupModelCopyWithImpl<$Res, SignupModel>;
  @useResult
  $Res call({
    @JsonKey(name: "access_token") String accessToken,
    @JsonKey(name: "token_type") String tokenType,
    @JsonKey(name: "expires_in") int expiresIn,
    @JsonKey(name: "expires_at") int expiresAt,
    @JsonKey(name: "refresh_token") String refreshToken,
    @JsonKey(name: "user") User user,
  });

  $UserCopyWith<$Res> get user;
}

/// @nodoc
class _$SignupModelCopyWithImpl<$Res, $Val extends SignupModel>
    implements $SignupModelCopyWith<$Res> {
  _$SignupModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of SignupModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? accessToken = null,
    Object? tokenType = null,
    Object? expiresIn = null,
    Object? expiresAt = null,
    Object? refreshToken = null,
    Object? user = null,
  }) {
    return _then(
      _value.copyWith(
            accessToken: null == accessToken
                ? _value.accessToken
                : accessToken // ignore: cast_nullable_to_non_nullable
                      as String,
            tokenType: null == tokenType
                ? _value.tokenType
                : tokenType // ignore: cast_nullable_to_non_nullable
                      as String,
            expiresIn: null == expiresIn
                ? _value.expiresIn
                : expiresIn // ignore: cast_nullable_to_non_nullable
                      as int,
            expiresAt: null == expiresAt
                ? _value.expiresAt
                : expiresAt // ignore: cast_nullable_to_non_nullable
                      as int,
            refreshToken: null == refreshToken
                ? _value.refreshToken
                : refreshToken // ignore: cast_nullable_to_non_nullable
                      as String,
            user: null == user
                ? _value.user
                : user // ignore: cast_nullable_to_non_nullable
                      as User,
          )
          as $Val,
    );
  }

  /// Create a copy of SignupModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $UserCopyWith<$Res> get user {
    return $UserCopyWith<$Res>(_value.user, (value) {
      return _then(_value.copyWith(user: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$SignupModelImplCopyWith<$Res>
    implements $SignupModelCopyWith<$Res> {
  factory _$$SignupModelImplCopyWith(
    _$SignupModelImpl value,
    $Res Function(_$SignupModelImpl) then,
  ) = __$$SignupModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    @JsonKey(name: "access_token") String accessToken,
    @JsonKey(name: "token_type") String tokenType,
    @JsonKey(name: "expires_in") int expiresIn,
    @JsonKey(name: "expires_at") int expiresAt,
    @JsonKey(name: "refresh_token") String refreshToken,
    @JsonKey(name: "user") User user,
  });

  @override
  $UserCopyWith<$Res> get user;
}

/// @nodoc
class __$$SignupModelImplCopyWithImpl<$Res>
    extends _$SignupModelCopyWithImpl<$Res, _$SignupModelImpl>
    implements _$$SignupModelImplCopyWith<$Res> {
  __$$SignupModelImplCopyWithImpl(
    _$SignupModelImpl _value,
    $Res Function(_$SignupModelImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of SignupModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? accessToken = null,
    Object? tokenType = null,
    Object? expiresIn = null,
    Object? expiresAt = null,
    Object? refreshToken = null,
    Object? user = null,
  }) {
    return _then(
      _$SignupModelImpl(
        accessToken: null == accessToken
            ? _value.accessToken
            : accessToken // ignore: cast_nullable_to_non_nullable
                  as String,
        tokenType: null == tokenType
            ? _value.tokenType
            : tokenType // ignore: cast_nullable_to_non_nullable
                  as String,
        expiresIn: null == expiresIn
            ? _value.expiresIn
            : expiresIn // ignore: cast_nullable_to_non_nullable
                  as int,
        expiresAt: null == expiresAt
            ? _value.expiresAt
            : expiresAt // ignore: cast_nullable_to_non_nullable
                  as int,
        refreshToken: null == refreshToken
            ? _value.refreshToken
            : refreshToken // ignore: cast_nullable_to_non_nullable
                  as String,
        user: null == user
            ? _value.user
            : user // ignore: cast_nullable_to_non_nullable
                  as User,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$SignupModelImpl implements _SignupModel {
  const _$SignupModelImpl({
    @JsonKey(name: "access_token") required this.accessToken,
    @JsonKey(name: "token_type") required this.tokenType,
    @JsonKey(name: "expires_in") required this.expiresIn,
    @JsonKey(name: "expires_at") required this.expiresAt,
    @JsonKey(name: "refresh_token") required this.refreshToken,
    @JsonKey(name: "user") required this.user,
  });

  factory _$SignupModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$SignupModelImplFromJson(json);

  @override
  @JsonKey(name: "access_token")
  final String accessToken;
  @override
  @JsonKey(name: "token_type")
  final String tokenType;
  @override
  @JsonKey(name: "expires_in")
  final int expiresIn;
  @override
  @JsonKey(name: "expires_at")
  final int expiresAt;
  @override
  @JsonKey(name: "refresh_token")
  final String refreshToken;
  @override
  @JsonKey(name: "user")
  final User user;

  @override
  String toString() {
    return 'SignupModel(accessToken: $accessToken, tokenType: $tokenType, expiresIn: $expiresIn, expiresAt: $expiresAt, refreshToken: $refreshToken, user: $user)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SignupModelImpl &&
            (identical(other.accessToken, accessToken) ||
                other.accessToken == accessToken) &&
            (identical(other.tokenType, tokenType) ||
                other.tokenType == tokenType) &&
            (identical(other.expiresIn, expiresIn) ||
                other.expiresIn == expiresIn) &&
            (identical(other.expiresAt, expiresAt) ||
                other.expiresAt == expiresAt) &&
            (identical(other.refreshToken, refreshToken) ||
                other.refreshToken == refreshToken) &&
            (identical(other.user, user) || other.user == user));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    accessToken,
    tokenType,
    expiresIn,
    expiresAt,
    refreshToken,
    user,
  );

  /// Create a copy of SignupModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SignupModelImplCopyWith<_$SignupModelImpl> get copyWith =>
      __$$SignupModelImplCopyWithImpl<_$SignupModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SignupModelImplToJson(this);
  }
}

abstract class _SignupModel implements SignupModel {
  const factory _SignupModel({
    @JsonKey(name: "access_token") required final String accessToken,
    @JsonKey(name: "token_type") required final String tokenType,
    @JsonKey(name: "expires_in") required final int expiresIn,
    @JsonKey(name: "expires_at") required final int expiresAt,
    @JsonKey(name: "refresh_token") required final String refreshToken,
    @JsonKey(name: "user") required final User user,
  }) = _$SignupModelImpl;

  factory _SignupModel.fromJson(Map<String, dynamic> json) =
      _$SignupModelImpl.fromJson;

  @override
  @JsonKey(name: "access_token")
  String get accessToken;
  @override
  @JsonKey(name: "token_type")
  String get tokenType;
  @override
  @JsonKey(name: "expires_in")
  int get expiresIn;
  @override
  @JsonKey(name: "expires_at")
  int get expiresAt;
  @override
  @JsonKey(name: "refresh_token")
  String get refreshToken;
  @override
  @JsonKey(name: "user")
  User get user;

  /// Create a copy of SignupModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SignupModelImplCopyWith<_$SignupModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

User _$UserFromJson(Map<String, dynamic> json) {
  return _User.fromJson(json);
}

/// @nodoc
mixin _$User {
  @JsonKey(name: "id")
  String get id => throw _privateConstructorUsedError;
  @JsonKey(name: "aud")
  String get aud => throw _privateConstructorUsedError;
  @JsonKey(name: "role")
  String get role => throw _privateConstructorUsedError;
  @JsonKey(name: "email")
  String get email => throw _privateConstructorUsedError;
  @JsonKey(name: "email_confirmed_at")
  String get emailConfirmedAt => throw _privateConstructorUsedError;
  @JsonKey(name: "phone")
  String get phone => throw _privateConstructorUsedError;
  @JsonKey(name: "last_sign_in_at")
  String get lastSignInAt => throw _privateConstructorUsedError;
  @JsonKey(name: "app_metadata")
  AppMetadata get appMetadata => throw _privateConstructorUsedError;
  @JsonKey(name: "user_metadata")
  Data get userMetadata => throw _privateConstructorUsedError;
  @JsonKey(name: "identities")
  List<Identity> get identities => throw _privateConstructorUsedError;
  @JsonKey(name: "created_at")
  String get createdAt => throw _privateConstructorUsedError;
  @JsonKey(name: "updated_at")
  String get updatedAt => throw _privateConstructorUsedError;
  @JsonKey(name: "is_anonymous")
  bool get isAnonymous => throw _privateConstructorUsedError;

  /// Serializes this User to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of User
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $UserCopyWith<User> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UserCopyWith<$Res> {
  factory $UserCopyWith(User value, $Res Function(User) then) =
      _$UserCopyWithImpl<$Res, User>;
  @useResult
  $Res call({
    @JsonKey(name: "id") String id,
    @JsonKey(name: "aud") String aud,
    @JsonKey(name: "role") String role,
    @JsonKey(name: "email") String email,
    @JsonKey(name: "email_confirmed_at") String emailConfirmedAt,
    @JsonKey(name: "phone") String phone,
    @JsonKey(name: "last_sign_in_at") String lastSignInAt,
    @JsonKey(name: "app_metadata") AppMetadata appMetadata,
    @JsonKey(name: "user_metadata") Data userMetadata,
    @JsonKey(name: "identities") List<Identity> identities,
    @JsonKey(name: "created_at") String createdAt,
    @JsonKey(name: "updated_at") String updatedAt,
    @JsonKey(name: "is_anonymous") bool isAnonymous,
  });

  $AppMetadataCopyWith<$Res> get appMetadata;
  $DataCopyWith<$Res> get userMetadata;
}

/// @nodoc
class _$UserCopyWithImpl<$Res, $Val extends User>
    implements $UserCopyWith<$Res> {
  _$UserCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of User
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? aud = null,
    Object? role = null,
    Object? email = null,
    Object? emailConfirmedAt = null,
    Object? phone = null,
    Object? lastSignInAt = null,
    Object? appMetadata = null,
    Object? userMetadata = null,
    Object? identities = null,
    Object? createdAt = null,
    Object? updatedAt = null,
    Object? isAnonymous = null,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            aud: null == aud
                ? _value.aud
                : aud // ignore: cast_nullable_to_non_nullable
                      as String,
            role: null == role
                ? _value.role
                : role // ignore: cast_nullable_to_non_nullable
                      as String,
            email: null == email
                ? _value.email
                : email // ignore: cast_nullable_to_non_nullable
                      as String,
            emailConfirmedAt: null == emailConfirmedAt
                ? _value.emailConfirmedAt
                : emailConfirmedAt // ignore: cast_nullable_to_non_nullable
                      as String,
            phone: null == phone
                ? _value.phone
                : phone // ignore: cast_nullable_to_non_nullable
                      as String,
            lastSignInAt: null == lastSignInAt
                ? _value.lastSignInAt
                : lastSignInAt // ignore: cast_nullable_to_non_nullable
                      as String,
            appMetadata: null == appMetadata
                ? _value.appMetadata
                : appMetadata // ignore: cast_nullable_to_non_nullable
                      as AppMetadata,
            userMetadata: null == userMetadata
                ? _value.userMetadata
                : userMetadata // ignore: cast_nullable_to_non_nullable
                      as Data,
            identities: null == identities
                ? _value.identities
                : identities // ignore: cast_nullable_to_non_nullable
                      as List<Identity>,
            createdAt: null == createdAt
                ? _value.createdAt
                : createdAt // ignore: cast_nullable_to_non_nullable
                      as String,
            updatedAt: null == updatedAt
                ? _value.updatedAt
                : updatedAt // ignore: cast_nullable_to_non_nullable
                      as String,
            isAnonymous: null == isAnonymous
                ? _value.isAnonymous
                : isAnonymous // ignore: cast_nullable_to_non_nullable
                      as bool,
          )
          as $Val,
    );
  }

  /// Create a copy of User
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $AppMetadataCopyWith<$Res> get appMetadata {
    return $AppMetadataCopyWith<$Res>(_value.appMetadata, (value) {
      return _then(_value.copyWith(appMetadata: value) as $Val);
    });
  }

  /// Create a copy of User
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $DataCopyWith<$Res> get userMetadata {
    return $DataCopyWith<$Res>(_value.userMetadata, (value) {
      return _then(_value.copyWith(userMetadata: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$UserImplCopyWith<$Res> implements $UserCopyWith<$Res> {
  factory _$$UserImplCopyWith(
    _$UserImpl value,
    $Res Function(_$UserImpl) then,
  ) = __$$UserImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    @JsonKey(name: "id") String id,
    @JsonKey(name: "aud") String aud,
    @JsonKey(name: "role") String role,
    @JsonKey(name: "email") String email,
    @JsonKey(name: "email_confirmed_at") String emailConfirmedAt,
    @JsonKey(name: "phone") String phone,
    @JsonKey(name: "last_sign_in_at") String lastSignInAt,
    @JsonKey(name: "app_metadata") AppMetadata appMetadata,
    @JsonKey(name: "user_metadata") Data userMetadata,
    @JsonKey(name: "identities") List<Identity> identities,
    @JsonKey(name: "created_at") String createdAt,
    @JsonKey(name: "updated_at") String updatedAt,
    @JsonKey(name: "is_anonymous") bool isAnonymous,
  });

  @override
  $AppMetadataCopyWith<$Res> get appMetadata;
  @override
  $DataCopyWith<$Res> get userMetadata;
}

/// @nodoc
class __$$UserImplCopyWithImpl<$Res>
    extends _$UserCopyWithImpl<$Res, _$UserImpl>
    implements _$$UserImplCopyWith<$Res> {
  __$$UserImplCopyWithImpl(_$UserImpl _value, $Res Function(_$UserImpl) _then)
    : super(_value, _then);

  /// Create a copy of User
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? aud = null,
    Object? role = null,
    Object? email = null,
    Object? emailConfirmedAt = null,
    Object? phone = null,
    Object? lastSignInAt = null,
    Object? appMetadata = null,
    Object? userMetadata = null,
    Object? identities = null,
    Object? createdAt = null,
    Object? updatedAt = null,
    Object? isAnonymous = null,
  }) {
    return _then(
      _$UserImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        aud: null == aud
            ? _value.aud
            : aud // ignore: cast_nullable_to_non_nullable
                  as String,
        role: null == role
            ? _value.role
            : role // ignore: cast_nullable_to_non_nullable
                  as String,
        email: null == email
            ? _value.email
            : email // ignore: cast_nullable_to_non_nullable
                  as String,
        emailConfirmedAt: null == emailConfirmedAt
            ? _value.emailConfirmedAt
            : emailConfirmedAt // ignore: cast_nullable_to_non_nullable
                  as String,
        phone: null == phone
            ? _value.phone
            : phone // ignore: cast_nullable_to_non_nullable
                  as String,
        lastSignInAt: null == lastSignInAt
            ? _value.lastSignInAt
            : lastSignInAt // ignore: cast_nullable_to_non_nullable
                  as String,
        appMetadata: null == appMetadata
            ? _value.appMetadata
            : appMetadata // ignore: cast_nullable_to_non_nullable
                  as AppMetadata,
        userMetadata: null == userMetadata
            ? _value.userMetadata
            : userMetadata // ignore: cast_nullable_to_non_nullable
                  as Data,
        identities: null == identities
            ? _value._identities
            : identities // ignore: cast_nullable_to_non_nullable
                  as List<Identity>,
        createdAt: null == createdAt
            ? _value.createdAt
            : createdAt // ignore: cast_nullable_to_non_nullable
                  as String,
        updatedAt: null == updatedAt
            ? _value.updatedAt
            : updatedAt // ignore: cast_nullable_to_non_nullable
                  as String,
        isAnonymous: null == isAnonymous
            ? _value.isAnonymous
            : isAnonymous // ignore: cast_nullable_to_non_nullable
                  as bool,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$UserImpl implements _User {
  const _$UserImpl({
    @JsonKey(name: "id") required this.id,
    @JsonKey(name: "aud") required this.aud,
    @JsonKey(name: "role") required this.role,
    @JsonKey(name: "email") required this.email,
    @JsonKey(name: "email_confirmed_at") required this.emailConfirmedAt,
    @JsonKey(name: "phone") required this.phone,
    @JsonKey(name: "last_sign_in_at") required this.lastSignInAt,
    @JsonKey(name: "app_metadata") required this.appMetadata,
    @JsonKey(name: "user_metadata") required this.userMetadata,
    @JsonKey(name: "identities") required final List<Identity> identities,
    @JsonKey(name: "created_at") required this.createdAt,
    @JsonKey(name: "updated_at") required this.updatedAt,
    @JsonKey(name: "is_anonymous") required this.isAnonymous,
  }) : _identities = identities;

  factory _$UserImpl.fromJson(Map<String, dynamic> json) =>
      _$$UserImplFromJson(json);

  @override
  @JsonKey(name: "id")
  final String id;
  @override
  @JsonKey(name: "aud")
  final String aud;
  @override
  @JsonKey(name: "role")
  final String role;
  @override
  @JsonKey(name: "email")
  final String email;
  @override
  @JsonKey(name: "email_confirmed_at")
  final String emailConfirmedAt;
  @override
  @JsonKey(name: "phone")
  final String phone;
  @override
  @JsonKey(name: "last_sign_in_at")
  final String lastSignInAt;
  @override
  @JsonKey(name: "app_metadata")
  final AppMetadata appMetadata;
  @override
  @JsonKey(name: "user_metadata")
  final Data userMetadata;
  final List<Identity> _identities;
  @override
  @JsonKey(name: "identities")
  List<Identity> get identities {
    if (_identities is EqualUnmodifiableListView) return _identities;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_identities);
  }

  @override
  @JsonKey(name: "created_at")
  final String createdAt;
  @override
  @JsonKey(name: "updated_at")
  final String updatedAt;
  @override
  @JsonKey(name: "is_anonymous")
  final bool isAnonymous;

  @override
  String toString() {
    return 'User(id: $id, aud: $aud, role: $role, email: $email, emailConfirmedAt: $emailConfirmedAt, phone: $phone, lastSignInAt: $lastSignInAt, appMetadata: $appMetadata, userMetadata: $userMetadata, identities: $identities, createdAt: $createdAt, updatedAt: $updatedAt, isAnonymous: $isAnonymous)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UserImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.aud, aud) || other.aud == aud) &&
            (identical(other.role, role) || other.role == role) &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.emailConfirmedAt, emailConfirmedAt) ||
                other.emailConfirmedAt == emailConfirmedAt) &&
            (identical(other.phone, phone) || other.phone == phone) &&
            (identical(other.lastSignInAt, lastSignInAt) ||
                other.lastSignInAt == lastSignInAt) &&
            (identical(other.appMetadata, appMetadata) ||
                other.appMetadata == appMetadata) &&
            (identical(other.userMetadata, userMetadata) ||
                other.userMetadata == userMetadata) &&
            const DeepCollectionEquality().equals(
              other._identities,
              _identities,
            ) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt) &&
            (identical(other.isAnonymous, isAnonymous) ||
                other.isAnonymous == isAnonymous));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    aud,
    role,
    email,
    emailConfirmedAt,
    phone,
    lastSignInAt,
    appMetadata,
    userMetadata,
    const DeepCollectionEquality().hash(_identities),
    createdAt,
    updatedAt,
    isAnonymous,
  );

  /// Create a copy of User
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UserImplCopyWith<_$UserImpl> get copyWith =>
      __$$UserImplCopyWithImpl<_$UserImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$UserImplToJson(this);
  }
}

abstract class _User implements User {
  const factory _User({
    @JsonKey(name: "id") required final String id,
    @JsonKey(name: "aud") required final String aud,
    @JsonKey(name: "role") required final String role,
    @JsonKey(name: "email") required final String email,
    @JsonKey(name: "email_confirmed_at") required final String emailConfirmedAt,
    @JsonKey(name: "phone") required final String phone,
    @JsonKey(name: "last_sign_in_at") required final String lastSignInAt,
    @JsonKey(name: "app_metadata") required final AppMetadata appMetadata,
    @JsonKey(name: "user_metadata") required final Data userMetadata,
    @JsonKey(name: "identities") required final List<Identity> identities,
    @JsonKey(name: "created_at") required final String createdAt,
    @JsonKey(name: "updated_at") required final String updatedAt,
    @JsonKey(name: "is_anonymous") required final bool isAnonymous,
  }) = _$UserImpl;

  factory _User.fromJson(Map<String, dynamic> json) = _$UserImpl.fromJson;

  @override
  @JsonKey(name: "id")
  String get id;
  @override
  @JsonKey(name: "aud")
  String get aud;
  @override
  @JsonKey(name: "role")
  String get role;
  @override
  @JsonKey(name: "email")
  String get email;
  @override
  @JsonKey(name: "email_confirmed_at")
  String get emailConfirmedAt;
  @override
  @JsonKey(name: "phone")
  String get phone;
  @override
  @JsonKey(name: "last_sign_in_at")
  String get lastSignInAt;
  @override
  @JsonKey(name: "app_metadata")
  AppMetadata get appMetadata;
  @override
  @JsonKey(name: "user_metadata")
  Data get userMetadata;
  @override
  @JsonKey(name: "identities")
  List<Identity> get identities;
  @override
  @JsonKey(name: "created_at")
  String get createdAt;
  @override
  @JsonKey(name: "updated_at")
  String get updatedAt;
  @override
  @JsonKey(name: "is_anonymous")
  bool get isAnonymous;

  /// Create a copy of User
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UserImplCopyWith<_$UserImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

AppMetadata _$AppMetadataFromJson(Map<String, dynamic> json) {
  return _AppMetadata.fromJson(json);
}

/// @nodoc
mixin _$AppMetadata {
  @JsonKey(name: "provider")
  String get provider => throw _privateConstructorUsedError;
  @JsonKey(name: "providers")
  List<String> get providers => throw _privateConstructorUsedError;

  /// Serializes this AppMetadata to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of AppMetadata
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $AppMetadataCopyWith<AppMetadata> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AppMetadataCopyWith<$Res> {
  factory $AppMetadataCopyWith(
    AppMetadata value,
    $Res Function(AppMetadata) then,
  ) = _$AppMetadataCopyWithImpl<$Res, AppMetadata>;
  @useResult
  $Res call({
    @JsonKey(name: "provider") String provider,
    @JsonKey(name: "providers") List<String> providers,
  });
}

/// @nodoc
class _$AppMetadataCopyWithImpl<$Res, $Val extends AppMetadata>
    implements $AppMetadataCopyWith<$Res> {
  _$AppMetadataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AppMetadata
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? provider = null, Object? providers = null}) {
    return _then(
      _value.copyWith(
            provider: null == provider
                ? _value.provider
                : provider // ignore: cast_nullable_to_non_nullable
                      as String,
            providers: null == providers
                ? _value.providers
                : providers // ignore: cast_nullable_to_non_nullable
                      as List<String>,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$AppMetadataImplCopyWith<$Res>
    implements $AppMetadataCopyWith<$Res> {
  factory _$$AppMetadataImplCopyWith(
    _$AppMetadataImpl value,
    $Res Function(_$AppMetadataImpl) then,
  ) = __$$AppMetadataImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    @JsonKey(name: "provider") String provider,
    @JsonKey(name: "providers") List<String> providers,
  });
}

/// @nodoc
class __$$AppMetadataImplCopyWithImpl<$Res>
    extends _$AppMetadataCopyWithImpl<$Res, _$AppMetadataImpl>
    implements _$$AppMetadataImplCopyWith<$Res> {
  __$$AppMetadataImplCopyWithImpl(
    _$AppMetadataImpl _value,
    $Res Function(_$AppMetadataImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of AppMetadata
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? provider = null, Object? providers = null}) {
    return _then(
      _$AppMetadataImpl(
        provider: null == provider
            ? _value.provider
            : provider // ignore: cast_nullable_to_non_nullable
                  as String,
        providers: null == providers
            ? _value._providers
            : providers // ignore: cast_nullable_to_non_nullable
                  as List<String>,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$AppMetadataImpl implements _AppMetadata {
  const _$AppMetadataImpl({
    @JsonKey(name: "provider") required this.provider,
    @JsonKey(name: "providers") required final List<String> providers,
  }) : _providers = providers;

  factory _$AppMetadataImpl.fromJson(Map<String, dynamic> json) =>
      _$$AppMetadataImplFromJson(json);

  @override
  @JsonKey(name: "provider")
  final String provider;
  final List<String> _providers;
  @override
  @JsonKey(name: "providers")
  List<String> get providers {
    if (_providers is EqualUnmodifiableListView) return _providers;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_providers);
  }

  @override
  String toString() {
    return 'AppMetadata(provider: $provider, providers: $providers)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AppMetadataImpl &&
            (identical(other.provider, provider) ||
                other.provider == provider) &&
            const DeepCollectionEquality().equals(
              other._providers,
              _providers,
            ));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    provider,
    const DeepCollectionEquality().hash(_providers),
  );

  /// Create a copy of AppMetadata
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AppMetadataImplCopyWith<_$AppMetadataImpl> get copyWith =>
      __$$AppMetadataImplCopyWithImpl<_$AppMetadataImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AppMetadataImplToJson(this);
  }
}

abstract class _AppMetadata implements AppMetadata {
  const factory _AppMetadata({
    @JsonKey(name: "provider") required final String provider,
    @JsonKey(name: "providers") required final List<String> providers,
  }) = _$AppMetadataImpl;

  factory _AppMetadata.fromJson(Map<String, dynamic> json) =
      _$AppMetadataImpl.fromJson;

  @override
  @JsonKey(name: "provider")
  String get provider;
  @override
  @JsonKey(name: "providers")
  List<String> get providers;

  /// Create a copy of AppMetadata
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AppMetadataImplCopyWith<_$AppMetadataImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

Identity _$IdentityFromJson(Map<String, dynamic> json) {
  return _Identity.fromJson(json);
}

/// @nodoc
mixin _$Identity {
  @JsonKey(name: "identity_id")
  String get identityId => throw _privateConstructorUsedError;
  @JsonKey(name: "id")
  String get id => throw _privateConstructorUsedError;
  @JsonKey(name: "user_id")
  String get userId => throw _privateConstructorUsedError;
  @JsonKey(name: "identity_data")
  Data get identityData => throw _privateConstructorUsedError;
  @JsonKey(name: "provider")
  String get provider => throw _privateConstructorUsedError;
  @JsonKey(name: "last_sign_in_at")
  String get lastSignInAt => throw _privateConstructorUsedError;
  @JsonKey(name: "created_at")
  String get createdAt => throw _privateConstructorUsedError;
  @JsonKey(name: "updated_at")
  String get updatedAt => throw _privateConstructorUsedError;
  @JsonKey(name: "email")
  String get email => throw _privateConstructorUsedError;

  /// Serializes this Identity to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of Identity
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $IdentityCopyWith<Identity> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $IdentityCopyWith<$Res> {
  factory $IdentityCopyWith(Identity value, $Res Function(Identity) then) =
      _$IdentityCopyWithImpl<$Res, Identity>;
  @useResult
  $Res call({
    @JsonKey(name: "identity_id") String identityId,
    @JsonKey(name: "id") String id,
    @JsonKey(name: "user_id") String userId,
    @JsonKey(name: "identity_data") Data identityData,
    @JsonKey(name: "provider") String provider,
    @JsonKey(name: "last_sign_in_at") String lastSignInAt,
    @JsonKey(name: "created_at") String createdAt,
    @JsonKey(name: "updated_at") String updatedAt,
    @JsonKey(name: "email") String email,
  });

  $DataCopyWith<$Res> get identityData;
}

/// @nodoc
class _$IdentityCopyWithImpl<$Res, $Val extends Identity>
    implements $IdentityCopyWith<$Res> {
  _$IdentityCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of Identity
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? identityId = null,
    Object? id = null,
    Object? userId = null,
    Object? identityData = null,
    Object? provider = null,
    Object? lastSignInAt = null,
    Object? createdAt = null,
    Object? updatedAt = null,
    Object? email = null,
  }) {
    return _then(
      _value.copyWith(
            identityId: null == identityId
                ? _value.identityId
                : identityId // ignore: cast_nullable_to_non_nullable
                      as String,
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            userId: null == userId
                ? _value.userId
                : userId // ignore: cast_nullable_to_non_nullable
                      as String,
            identityData: null == identityData
                ? _value.identityData
                : identityData // ignore: cast_nullable_to_non_nullable
                      as Data,
            provider: null == provider
                ? _value.provider
                : provider // ignore: cast_nullable_to_non_nullable
                      as String,
            lastSignInAt: null == lastSignInAt
                ? _value.lastSignInAt
                : lastSignInAt // ignore: cast_nullable_to_non_nullable
                      as String,
            createdAt: null == createdAt
                ? _value.createdAt
                : createdAt // ignore: cast_nullable_to_non_nullable
                      as String,
            updatedAt: null == updatedAt
                ? _value.updatedAt
                : updatedAt // ignore: cast_nullable_to_non_nullable
                      as String,
            email: null == email
                ? _value.email
                : email // ignore: cast_nullable_to_non_nullable
                      as String,
          )
          as $Val,
    );
  }

  /// Create a copy of Identity
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $DataCopyWith<$Res> get identityData {
    return $DataCopyWith<$Res>(_value.identityData, (value) {
      return _then(_value.copyWith(identityData: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$IdentityImplCopyWith<$Res>
    implements $IdentityCopyWith<$Res> {
  factory _$$IdentityImplCopyWith(
    _$IdentityImpl value,
    $Res Function(_$IdentityImpl) then,
  ) = __$$IdentityImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    @JsonKey(name: "identity_id") String identityId,
    @JsonKey(name: "id") String id,
    @JsonKey(name: "user_id") String userId,
    @JsonKey(name: "identity_data") Data identityData,
    @JsonKey(name: "provider") String provider,
    @JsonKey(name: "last_sign_in_at") String lastSignInAt,
    @JsonKey(name: "created_at") String createdAt,
    @JsonKey(name: "updated_at") String updatedAt,
    @JsonKey(name: "email") String email,
  });

  @override
  $DataCopyWith<$Res> get identityData;
}

/// @nodoc
class __$$IdentityImplCopyWithImpl<$Res>
    extends _$IdentityCopyWithImpl<$Res, _$IdentityImpl>
    implements _$$IdentityImplCopyWith<$Res> {
  __$$IdentityImplCopyWithImpl(
    _$IdentityImpl _value,
    $Res Function(_$IdentityImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of Identity
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? identityId = null,
    Object? id = null,
    Object? userId = null,
    Object? identityData = null,
    Object? provider = null,
    Object? lastSignInAt = null,
    Object? createdAt = null,
    Object? updatedAt = null,
    Object? email = null,
  }) {
    return _then(
      _$IdentityImpl(
        identityId: null == identityId
            ? _value.identityId
            : identityId // ignore: cast_nullable_to_non_nullable
                  as String,
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        userId: null == userId
            ? _value.userId
            : userId // ignore: cast_nullable_to_non_nullable
                  as String,
        identityData: null == identityData
            ? _value.identityData
            : identityData // ignore: cast_nullable_to_non_nullable
                  as Data,
        provider: null == provider
            ? _value.provider
            : provider // ignore: cast_nullable_to_non_nullable
                  as String,
        lastSignInAt: null == lastSignInAt
            ? _value.lastSignInAt
            : lastSignInAt // ignore: cast_nullable_to_non_nullable
                  as String,
        createdAt: null == createdAt
            ? _value.createdAt
            : createdAt // ignore: cast_nullable_to_non_nullable
                  as String,
        updatedAt: null == updatedAt
            ? _value.updatedAt
            : updatedAt // ignore: cast_nullable_to_non_nullable
                  as String,
        email: null == email
            ? _value.email
            : email // ignore: cast_nullable_to_non_nullable
                  as String,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$IdentityImpl implements _Identity {
  const _$IdentityImpl({
    @JsonKey(name: "identity_id") required this.identityId,
    @JsonKey(name: "id") required this.id,
    @JsonKey(name: "user_id") required this.userId,
    @JsonKey(name: "identity_data") required this.identityData,
    @JsonKey(name: "provider") required this.provider,
    @JsonKey(name: "last_sign_in_at") required this.lastSignInAt,
    @JsonKey(name: "created_at") required this.createdAt,
    @JsonKey(name: "updated_at") required this.updatedAt,
    @JsonKey(name: "email") required this.email,
  });

  factory _$IdentityImpl.fromJson(Map<String, dynamic> json) =>
      _$$IdentityImplFromJson(json);

  @override
  @JsonKey(name: "identity_id")
  final String identityId;
  @override
  @JsonKey(name: "id")
  final String id;
  @override
  @JsonKey(name: "user_id")
  final String userId;
  @override
  @JsonKey(name: "identity_data")
  final Data identityData;
  @override
  @JsonKey(name: "provider")
  final String provider;
  @override
  @JsonKey(name: "last_sign_in_at")
  final String lastSignInAt;
  @override
  @JsonKey(name: "created_at")
  final String createdAt;
  @override
  @JsonKey(name: "updated_at")
  final String updatedAt;
  @override
  @JsonKey(name: "email")
  final String email;

  @override
  String toString() {
    return 'Identity(identityId: $identityId, id: $id, userId: $userId, identityData: $identityData, provider: $provider, lastSignInAt: $lastSignInAt, createdAt: $createdAt, updatedAt: $updatedAt, email: $email)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$IdentityImpl &&
            (identical(other.identityId, identityId) ||
                other.identityId == identityId) &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.identityData, identityData) ||
                other.identityData == identityData) &&
            (identical(other.provider, provider) ||
                other.provider == provider) &&
            (identical(other.lastSignInAt, lastSignInAt) ||
                other.lastSignInAt == lastSignInAt) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt) &&
            (identical(other.email, email) || other.email == email));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    identityId,
    id,
    userId,
    identityData,
    provider,
    lastSignInAt,
    createdAt,
    updatedAt,
    email,
  );

  /// Create a copy of Identity
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$IdentityImplCopyWith<_$IdentityImpl> get copyWith =>
      __$$IdentityImplCopyWithImpl<_$IdentityImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$IdentityImplToJson(this);
  }
}

abstract class _Identity implements Identity {
  const factory _Identity({
    @JsonKey(name: "identity_id") required final String identityId,
    @JsonKey(name: "id") required final String id,
    @JsonKey(name: "user_id") required final String userId,
    @JsonKey(name: "identity_data") required final Data identityData,
    @JsonKey(name: "provider") required final String provider,
    @JsonKey(name: "last_sign_in_at") required final String lastSignInAt,
    @JsonKey(name: "created_at") required final String createdAt,
    @JsonKey(name: "updated_at") required final String updatedAt,
    @JsonKey(name: "email") required final String email,
  }) = _$IdentityImpl;

  factory _Identity.fromJson(Map<String, dynamic> json) =
      _$IdentityImpl.fromJson;

  @override
  @JsonKey(name: "identity_id")
  String get identityId;
  @override
  @JsonKey(name: "id")
  String get id;
  @override
  @JsonKey(name: "user_id")
  String get userId;
  @override
  @JsonKey(name: "identity_data")
  Data get identityData;
  @override
  @JsonKey(name: "provider")
  String get provider;
  @override
  @JsonKey(name: "last_sign_in_at")
  String get lastSignInAt;
  @override
  @JsonKey(name: "created_at")
  String get createdAt;
  @override
  @JsonKey(name: "updated_at")
  String get updatedAt;
  @override
  @JsonKey(name: "email")
  String get email;

  /// Create a copy of Identity
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$IdentityImplCopyWith<_$IdentityImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

Data _$DataFromJson(Map<String, dynamic> json) {
  return _Data.fromJson(json);
}

/// @nodoc
mixin _$Data {
  @JsonKey(name: "address")
  String get address => throw _privateConstructorUsedError;
  @JsonKey(name: "avatar_url")
  String get avatarUrl => throw _privateConstructorUsedError;
  @JsonKey(name: "email")
  String get email => throw _privateConstructorUsedError;
  @JsonKey(name: "email_verified")
  bool get emailVerified => throw _privateConstructorUsedError;
  @JsonKey(name: "first_name")
  String get firstName => throw _privateConstructorUsedError;
  @JsonKey(name: "last_name")
  String get lastName => throw _privateConstructorUsedError;
  @JsonKey(name: "party_id")
  String get partyId => throw _privateConstructorUsedError;
  @JsonKey(name: "party_type_key")
  String get partyTypeKey => throw _privateConstructorUsedError;
  @JsonKey(name: "phone")
  String get phone => throw _privateConstructorUsedError;
  @JsonKey(name: "phone_verified")
  bool get phoneVerified => throw _privateConstructorUsedError;
  @JsonKey(name: "sub")
  String get sub => throw _privateConstructorUsedError;
  @JsonKey(name: "tenant_code")
  String get tenantCode => throw _privateConstructorUsedError;

  /// Serializes this Data to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of Data
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $DataCopyWith<Data> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DataCopyWith<$Res> {
  factory $DataCopyWith(Data value, $Res Function(Data) then) =
      _$DataCopyWithImpl<$Res, Data>;
  @useResult
  $Res call({
    @JsonKey(name: "address") String address,
    @JsonKey(name: "avatar_url") String avatarUrl,
    @JsonKey(name: "email") String email,
    @JsonKey(name: "email_verified") bool emailVerified,
    @JsonKey(name: "first_name") String firstName,
    @JsonKey(name: "last_name") String lastName,
    @JsonKey(name: "party_id") String partyId,
    @JsonKey(name: "party_type_key") String partyTypeKey,
    @JsonKey(name: "phone") String phone,
    @JsonKey(name: "phone_verified") bool phoneVerified,
    @JsonKey(name: "sub") String sub,
    @JsonKey(name: "tenant_code") String tenantCode,
  });
}

/// @nodoc
class _$DataCopyWithImpl<$Res, $Val extends Data>
    implements $DataCopyWith<$Res> {
  _$DataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of Data
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? address = null,
    Object? avatarUrl = null,
    Object? email = null,
    Object? emailVerified = null,
    Object? firstName = null,
    Object? lastName = null,
    Object? partyId = null,
    Object? partyTypeKey = null,
    Object? phone = null,
    Object? phoneVerified = null,
    Object? sub = null,
    Object? tenantCode = null,
  }) {
    return _then(
      _value.copyWith(
            address: null == address
                ? _value.address
                : address // ignore: cast_nullable_to_non_nullable
                      as String,
            avatarUrl: null == avatarUrl
                ? _value.avatarUrl
                : avatarUrl // ignore: cast_nullable_to_non_nullable
                      as String,
            email: null == email
                ? _value.email
                : email // ignore: cast_nullable_to_non_nullable
                      as String,
            emailVerified: null == emailVerified
                ? _value.emailVerified
                : emailVerified // ignore: cast_nullable_to_non_nullable
                      as bool,
            firstName: null == firstName
                ? _value.firstName
                : firstName // ignore: cast_nullable_to_non_nullable
                      as String,
            lastName: null == lastName
                ? _value.lastName
                : lastName // ignore: cast_nullable_to_non_nullable
                      as String,
            partyId: null == partyId
                ? _value.partyId
                : partyId // ignore: cast_nullable_to_non_nullable
                      as String,
            partyTypeKey: null == partyTypeKey
                ? _value.partyTypeKey
                : partyTypeKey // ignore: cast_nullable_to_non_nullable
                      as String,
            phone: null == phone
                ? _value.phone
                : phone // ignore: cast_nullable_to_non_nullable
                      as String,
            phoneVerified: null == phoneVerified
                ? _value.phoneVerified
                : phoneVerified // ignore: cast_nullable_to_non_nullable
                      as bool,
            sub: null == sub
                ? _value.sub
                : sub // ignore: cast_nullable_to_non_nullable
                      as String,
            tenantCode: null == tenantCode
                ? _value.tenantCode
                : tenantCode // ignore: cast_nullable_to_non_nullable
                      as String,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$DataImplCopyWith<$Res> implements $DataCopyWith<$Res> {
  factory _$$DataImplCopyWith(
    _$DataImpl value,
    $Res Function(_$DataImpl) then,
  ) = __$$DataImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    @JsonKey(name: "address") String address,
    @JsonKey(name: "avatar_url") String avatarUrl,
    @JsonKey(name: "email") String email,
    @JsonKey(name: "email_verified") bool emailVerified,
    @JsonKey(name: "first_name") String firstName,
    @JsonKey(name: "last_name") String lastName,
    @JsonKey(name: "party_id") String partyId,
    @JsonKey(name: "party_type_key") String partyTypeKey,
    @JsonKey(name: "phone") String phone,
    @JsonKey(name: "phone_verified") bool phoneVerified,
    @JsonKey(name: "sub") String sub,
    @JsonKey(name: "tenant_code") String tenantCode,
  });
}

/// @nodoc
class __$$DataImplCopyWithImpl<$Res>
    extends _$DataCopyWithImpl<$Res, _$DataImpl>
    implements _$$DataImplCopyWith<$Res> {
  __$$DataImplCopyWithImpl(_$DataImpl _value, $Res Function(_$DataImpl) _then)
    : super(_value, _then);

  /// Create a copy of Data
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? address = null,
    Object? avatarUrl = null,
    Object? email = null,
    Object? emailVerified = null,
    Object? firstName = null,
    Object? lastName = null,
    Object? partyId = null,
    Object? partyTypeKey = null,
    Object? phone = null,
    Object? phoneVerified = null,
    Object? sub = null,
    Object? tenantCode = null,
  }) {
    return _then(
      _$DataImpl(
        address: null == address
            ? _value.address
            : address // ignore: cast_nullable_to_non_nullable
                  as String,
        avatarUrl: null == avatarUrl
            ? _value.avatarUrl
            : avatarUrl // ignore: cast_nullable_to_non_nullable
                  as String,
        email: null == email
            ? _value.email
            : email // ignore: cast_nullable_to_non_nullable
                  as String,
        emailVerified: null == emailVerified
            ? _value.emailVerified
            : emailVerified // ignore: cast_nullable_to_non_nullable
                  as bool,
        firstName: null == firstName
            ? _value.firstName
            : firstName // ignore: cast_nullable_to_non_nullable
                  as String,
        lastName: null == lastName
            ? _value.lastName
            : lastName // ignore: cast_nullable_to_non_nullable
                  as String,
        partyId: null == partyId
            ? _value.partyId
            : partyId // ignore: cast_nullable_to_non_nullable
                  as String,
        partyTypeKey: null == partyTypeKey
            ? _value.partyTypeKey
            : partyTypeKey // ignore: cast_nullable_to_non_nullable
                  as String,
        phone: null == phone
            ? _value.phone
            : phone // ignore: cast_nullable_to_non_nullable
                  as String,
        phoneVerified: null == phoneVerified
            ? _value.phoneVerified
            : phoneVerified // ignore: cast_nullable_to_non_nullable
                  as bool,
        sub: null == sub
            ? _value.sub
            : sub // ignore: cast_nullable_to_non_nullable
                  as String,
        tenantCode: null == tenantCode
            ? _value.tenantCode
            : tenantCode // ignore: cast_nullable_to_non_nullable
                  as String,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$DataImpl implements _Data {
  const _$DataImpl({
    @JsonKey(name: "address") required this.address,
    @JsonKey(name: "avatar_url") required this.avatarUrl,
    @JsonKey(name: "email") required this.email,
    @JsonKey(name: "email_verified") required this.emailVerified,
    @JsonKey(name: "first_name") required this.firstName,
    @JsonKey(name: "last_name") required this.lastName,
    @JsonKey(name: "party_id") required this.partyId,
    @JsonKey(name: "party_type_key") required this.partyTypeKey,
    @JsonKey(name: "phone") required this.phone,
    @JsonKey(name: "phone_verified") required this.phoneVerified,
    @JsonKey(name: "sub") required this.sub,
    @JsonKey(name: "tenant_code") required this.tenantCode,
  });

  factory _$DataImpl.fromJson(Map<String, dynamic> json) =>
      _$$DataImplFromJson(json);

  @override
  @JsonKey(name: "address")
  final String address;
  @override
  @JsonKey(name: "avatar_url")
  final String avatarUrl;
  @override
  @JsonKey(name: "email")
  final String email;
  @override
  @JsonKey(name: "email_verified")
  final bool emailVerified;
  @override
  @JsonKey(name: "first_name")
  final String firstName;
  @override
  @JsonKey(name: "last_name")
  final String lastName;
  @override
  @JsonKey(name: "party_id")
  final String partyId;
  @override
  @JsonKey(name: "party_type_key")
  final String partyTypeKey;
  @override
  @JsonKey(name: "phone")
  final String phone;
  @override
  @JsonKey(name: "phone_verified")
  final bool phoneVerified;
  @override
  @JsonKey(name: "sub")
  final String sub;
  @override
  @JsonKey(name: "tenant_code")
  final String tenantCode;

  @override
  String toString() {
    return 'Data(address: $address, avatarUrl: $avatarUrl, email: $email, emailVerified: $emailVerified, firstName: $firstName, lastName: $lastName, partyId: $partyId, partyTypeKey: $partyTypeKey, phone: $phone, phoneVerified: $phoneVerified, sub: $sub, tenantCode: $tenantCode)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DataImpl &&
            (identical(other.address, address) || other.address == address) &&
            (identical(other.avatarUrl, avatarUrl) ||
                other.avatarUrl == avatarUrl) &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.emailVerified, emailVerified) ||
                other.emailVerified == emailVerified) &&
            (identical(other.firstName, firstName) ||
                other.firstName == firstName) &&
            (identical(other.lastName, lastName) ||
                other.lastName == lastName) &&
            (identical(other.partyId, partyId) || other.partyId == partyId) &&
            (identical(other.partyTypeKey, partyTypeKey) ||
                other.partyTypeKey == partyTypeKey) &&
            (identical(other.phone, phone) || other.phone == phone) &&
            (identical(other.phoneVerified, phoneVerified) ||
                other.phoneVerified == phoneVerified) &&
            (identical(other.sub, sub) || other.sub == sub) &&
            (identical(other.tenantCode, tenantCode) ||
                other.tenantCode == tenantCode));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    address,
    avatarUrl,
    email,
    emailVerified,
    firstName,
    lastName,
    partyId,
    partyTypeKey,
    phone,
    phoneVerified,
    sub,
    tenantCode,
  );

  /// Create a copy of Data
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DataImplCopyWith<_$DataImpl> get copyWith =>
      __$$DataImplCopyWithImpl<_$DataImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$DataImplToJson(this);
  }
}

abstract class _Data implements Data {
  const factory _Data({
    @JsonKey(name: "address") required final String address,
    @JsonKey(name: "avatar_url") required final String avatarUrl,
    @JsonKey(name: "email") required final String email,
    @JsonKey(name: "email_verified") required final bool emailVerified,
    @JsonKey(name: "first_name") required final String firstName,
    @JsonKey(name: "last_name") required final String lastName,
    @JsonKey(name: "party_id") required final String partyId,
    @JsonKey(name: "party_type_key") required final String partyTypeKey,
    @JsonKey(name: "phone") required final String phone,
    @JsonKey(name: "phone_verified") required final bool phoneVerified,
    @JsonKey(name: "sub") required final String sub,
    @JsonKey(name: "tenant_code") required final String tenantCode,
  }) = _$DataImpl;

  factory _Data.fromJson(Map<String, dynamic> json) = _$DataImpl.fromJson;

  @override
  @JsonKey(name: "address")
  String get address;
  @override
  @JsonKey(name: "avatar_url")
  String get avatarUrl;
  @override
  @JsonKey(name: "email")
  String get email;
  @override
  @JsonKey(name: "email_verified")
  bool get emailVerified;
  @override
  @JsonKey(name: "first_name")
  String get firstName;
  @override
  @JsonKey(name: "last_name")
  String get lastName;
  @override
  @JsonKey(name: "party_id")
  String get partyId;
  @override
  @JsonKey(name: "party_type_key")
  String get partyTypeKey;
  @override
  @JsonKey(name: "phone")
  String get phone;
  @override
  @JsonKey(name: "phone_verified")
  bool get phoneVerified;
  @override
  @JsonKey(name: "sub")
  String get sub;
  @override
  @JsonKey(name: "tenant_code")
  String get tenantCode;

  /// Create a copy of Data
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DataImplCopyWith<_$DataImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
