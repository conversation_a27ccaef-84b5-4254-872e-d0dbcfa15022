import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:country_code_picker/country_code_picker.dart';
import 'package:flutter/foundation.dart';

import '../../../../core/theme/app_theme.dart';
import '../../../../core/router/app_router.dart';
import '../../../../core/database/test_database.dart';
import '../../../../shared/presentation/widgets/responsive_layout.dart';
import '../bloc/auth/auth_bloc.dart';
import '../widgets/auth_form_field.dart';
import '../widgets/auth_button.dart';

class SignupPage extends StatefulWidget {
  const SignupPage({super.key});

  @override
  State<SignupPage> createState() => _SignupPageState();
}

class _SignupPageState extends State<SignupPage> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _lastNameController = TextEditingController();
  final _emailController = TextEditingController();
  final _usernameController = TextEditingController();
  final _phoneController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  final _tenantController = TextEditingController();
  bool _obscurePassword = true;
  bool _obscureConfirmPassword = true;
  bool _agreeToTerms = false;
  CountryCode? _selectedCountryCode;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: BlocListener<AuthBloc, AuthState>(
        listener: (context, state) {
          state.whenOrNull(
            signUpSuccess: (signupUser) {
              print(signupUser);
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(
                    'Account created successfully! Please check your email to verify your account.',
                  ),
                  backgroundColor: AppTheme.successColor,
                  duration: Duration(seconds: 4),
                ),
              );
              // Navigate to login page after successful signup
              context.go(AppRouter.login);
            },
            signUpError: (message) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(message),
                  backgroundColor: AppTheme.errorColor,
                ),
              );
            },
            error: (message) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(message),
                  backgroundColor: AppTheme.errorColor,
                ),
              );
            },
          );
        },
        child: ResponsiveLayout(
          mobile: _buildMobileLayout(),
          tablet: _buildTabletLayout(),
          desktop: _buildDesktopLayout(),
        ),
      ),
    );
  }

  Widget _buildMobileLayout() {
    return SafeArea(
      child: SingleChildScrollView(
        padding: EdgeInsets.all(AppTheme.spacing24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            SizedBox(height: AppTheme.spacing16),
            _buildHeader(),
            SizedBox(height: AppTheme.spacing32),
            _buildSignupForm(),
            SizedBox(height: AppTheme.spacing24),
            _buildFooter(),
          ],
        ),
      ),
    );
  }

  Widget _buildTabletLayout() {
    return SafeArea(
      child: Center(
        child: SingleChildScrollView(
          child: ResponsiveContainer(
            maxWidth: 500.w,
            padding: EdgeInsets.all(AppTheme.spacing32),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                _buildHeader(),
                SizedBox(height: AppTheme.spacing32),
                _buildSignupForm(),
                SizedBox(height: AppTheme.spacing24),
                _buildFooter(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildDesktopLayout() {
    return SafeArea(
      child: Row(
        children: [
          // Left side - Branding
          Expanded(
            child: Container(
              color: AppTheme.primaryColor,
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.psychology,
                      size: 120.sp,
                      color: AppTheme.textOnPrimary,
                    ),
                    SizedBox(height: AppTheme.spacing24),
                    ResponsiveText(
                      'Join RecallLoop',
                      style: AppTheme.displayLarge.copyWith(
                        color: AppTheme.textOnPrimary,
                        fontWeight: FontWeight.w800,
                      ),
                    ),
                    SizedBox(height: AppTheme.spacing16),
                    ResponsiveText(
                      'Start your cognitive enhancement journey',
                      style: AppTheme.bodyLarge.copyWith(
                        color: AppTheme.textOnPrimary.withOpacity(0.9),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          // Right side - Signup form
          Expanded(
            child: Center(
              child: SingleChildScrollView(
                child: ResponsiveContainer(
                  maxWidth: 400.w,
                  padding: EdgeInsets.all(AppTheme.spacing40),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      _buildHeader(showLogo: false),
                      SizedBox(height: AppTheme.spacing32),
                      _buildSignupForm(),
                      SizedBox(height: AppTheme.spacing24),
                      _buildFooter(),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader({bool showLogo = true}) {
    return Column(
      children: [
        if (showLogo) ...[
          Container(
            width: 80.w,
            height: 80.h,
            decoration: BoxDecoration(
              color: AppTheme.primaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(AppTheme.radiusLarge),
            ),
            child: Icon(
              Icons.psychology,
              size: 40.sp,
              color: AppTheme.primaryColor,
            ),
          ),
          SizedBox(height: AppTheme.spacing24),
        ],
        ResponsiveText(
          'Create Account',
          style: AppTheme.displayMedium.copyWith(
            fontWeight: FontWeight.w700,
            color: AppTheme.textPrimary,
          ),
          textAlign: TextAlign.center,
        ),
        SizedBox(height: AppTheme.spacing8),
        ResponsiveText(
          'Join thousands improving their cognitive abilities',
          style: AppTheme.bodyMedium.copyWith(color: AppTheme.textSecondary),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildSignupForm() {
    return BlocBuilder<AuthBloc, AuthState>(
      builder: (context, state) {
        final isLoading = state.maybeWhen(
          loading: () => true,
          orElse: () => false,
        );

        return Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              AuthFormField(
                controller: _nameController,
                label: 'First Name',
                hintText: 'Enter your first name',
                prefixIcon: Icons.person_outline,
                enabled: !isLoading,
                textCapitalization: TextCapitalization.words,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter your full name';
                  }
                  if (value.trim().length < 2) {
                    return 'Name must be at least 2 characters';
                  }
                  return null;
                },
              ),
              SizedBox(height: AppTheme.spacing16),
              AuthFormField(
                controller: _lastNameController,
                label: 'Last Name',
                hintText: 'Enter your last name',
                prefixIcon: Icons.person_outline,
                enabled: !isLoading,
                textCapitalization: TextCapitalization.words,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter your last name';
                  }
                  if (value.trim().length < 2) {
                    return 'Name must be at least 1 characters';
                  }
                  return null;
                },
              ),
              SizedBox(height: AppTheme.spacing16),
              AuthFormField(
                controller: _emailController,
                label: 'Email',
                hintText: 'Enter your email',
                keyboardType: TextInputType.emailAddress,
                prefixIcon: Icons.email_outlined,
                enabled: !isLoading,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter your email';
                  }
                  if (!RegExp(
                    r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$',
                  ).hasMatch(value)) {
                    return 'Please enter a valid email';
                  }
                  return null;
                },
              ),
              SizedBox(height: AppTheme.spacing16),
              AuthFormField(
                controller: _usernameController,
                label: 'Address',
                hintText: 'Enter address',
                prefixIcon: Icons.alternate_email,
                enabled: !isLoading,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter an address';
                  }
                  return null;
                },
              ),
              SizedBox(height: AppTheme.spacing16),
              AuthPhoneField(
                controller: _phoneController,
                label: 'Phone Number',
                hintText: 'Enter your phone number',
                enabled: !isLoading,
                initialCountryCode: _selectedCountryCode,
                onCountryChanged: (countryCode) {
                  setState(() {
                    _selectedCountryCode = countryCode;
                  });
                },
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter your phone number';
                  }
                  // Remove any non-digit characters for validation
                  String digitsOnly = value.replaceAll(RegExp(r'[^0-9]'), '');
                  if (digitsOnly.length != 10) {
                    return 'Phone number must be exactly 10 digits';
                  }
                  return null;
                },
              ),
              SizedBox(height: AppTheme.spacing16),
              AuthFormField(
                controller: _passwordController,
                label: 'Password',
                hintText: 'Create a strong password',
                obscureText: _obscurePassword,
                prefixIcon: Icons.lock_outline,
                enabled: !isLoading,
                suffixIcon: IconButton(
                  icon: Icon(
                    _obscurePassword ? Icons.visibility : Icons.visibility_off,
                    color: AppTheme.textTertiary,
                  ),
                  onPressed: () {
                    setState(() {
                      _obscurePassword = !_obscurePassword;
                    });
                  },
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter a password';
                  }
                  if (value.length < 8) {
                    return 'Password must be at least 8 characters';
                  }
                  if (!RegExp(
                    r'^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)',
                  ).hasMatch(value)) {
                    return 'Password must contain uppercase, lowercase, and number';
                  }
                  return null;
                },
              ),
              SizedBox(height: AppTheme.spacing16),
              AuthFormField(
                controller: _confirmPasswordController,
                label: 'Confirm Password',
                hintText: 'Confirm your password',
                obscureText: _obscureConfirmPassword,
                prefixIcon: Icons.lock_outline,
                enabled: !isLoading,
                suffixIcon: IconButton(
                  icon: Icon(
                    _obscureConfirmPassword
                        ? Icons.visibility
                        : Icons.visibility_off,
                    color: AppTheme.textTertiary,
                  ),
                  onPressed: () {
                    setState(() {
                      _obscureConfirmPassword = !_obscureConfirmPassword;
                    });
                  },
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please confirm your password';
                  }
                  if (value != _passwordController.text) {
                    return 'Passwords do not match';
                  }
                  return null;
                },
              ),
              SizedBox(height: AppTheme.spacing16),
              AuthFormField(
                controller: _tenantController,
                label: 'Tenant Code (Optional)',
                hintText: 'Enter your tenant code',
                prefixIcon: Icons.business_outlined,
                enabled: !isLoading,
              ),
              SizedBox(height: AppTheme.spacing16),
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Checkbox(
                    value: _agreeToTerms,
                    onChanged: isLoading
                        ? null
                        : (value) {
                            setState(() {
                              _agreeToTerms = value ?? false;
                            });
                          },
                    activeColor: AppTheme.primaryColor,
                  ),
                  Expanded(
                    child: Padding(
                      padding: EdgeInsets.only(top: 12.h),
                      child: RichText(
                        text: TextSpan(
                          style: AppTheme.bodySmall.copyWith(
                            color: AppTheme.textSecondary,
                          ),
                          children: [
                            const TextSpan(text: 'I agree to the '),
                            TextSpan(
                              text: 'Terms of Service',
                              style: AppTheme.bodySmall.copyWith(
                                color: AppTheme.primaryColor,
                                decoration: TextDecoration.underline,
                              ),
                            ),
                            const TextSpan(text: ' and '),
                            TextSpan(
                              text: 'Privacy Policy',
                              style: AppTheme.bodySmall.copyWith(
                                color: AppTheme.primaryColor,
                                decoration: TextDecoration.underline,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              SizedBox(height: AppTheme.spacing32),
              AuthButton(
                text: 'Create Account',
                isLoading: isLoading,
                onPressed: _agreeToTerms
                    ? () {
                        if (_formKey.currentState!.validate()) {
                          context.read<AuthBloc>().add(
                            AuthEvent.signUpRequested(
                              firstName: _nameController.text.trim(),
                              lastName: _lastNameController.text.trim(),
                              email: _emailController.text.trim(),
                              address: _usernameController.text.trim(),
                              phone: _selectedCountryCode != null
                                  ? _selectedCountryCode!.dialCode!
                                  : '' + _phoneController.text.trim(),
                              password: _passwordController.text,
                            ),
                          );
                        }
                      }
                    : null,
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildFooter() {
    return Column(
      children: [
        Row(
          children: [
            Expanded(child: Divider(color: AppTheme.dividerColor)),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: AppTheme.spacing16),
              child: ResponsiveText(
                'OR',
                style: AppTheme.labelSmall.copyWith(
                  color: AppTheme.textTertiary,
                ),
              ),
            ),
            Expanded(child: Divider(color: AppTheme.dividerColor)),
          ],
        ),
        SizedBox(height: AppTheme.spacing24),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            ResponsiveText(
              'Already have an account? ',
              style: AppTheme.bodyMedium.copyWith(
                color: AppTheme.textSecondary,
              ),
            ),
            TextButton(
              onPressed: () {
                context.go(AppRouter.login);
              },
              child: ResponsiveText(
                'Sign In',
                style: AppTheme.labelMedium.copyWith(
                  color: AppTheme.primaryColor,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  @override
  void dispose() {
    _nameController.dispose();
    _lastNameController.dispose();
    _emailController.dispose();
    _usernameController.dispose();
    _phoneController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    _tenantController.dispose();
    super.dispose();
  }
}
