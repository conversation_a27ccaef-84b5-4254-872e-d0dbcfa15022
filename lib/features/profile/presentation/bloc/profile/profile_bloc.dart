import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

import '../../../domain/entities/user_profile.dart';

part 'profile_bloc.freezed.dart';
part 'profile_event.dart';
part 'profile_state.dart';

class ProfileBloc extends Bloc<ProfileEvent, ProfileState> {
  ProfileBloc() : super(const _Initial()) {
    on<ProfileEvent>((event, emit) async {
      try {
        if (event is _LoadRequested) {
          emit(const _Loading());

          // Simulate API call delay
          await Future.delayed(const Duration(seconds: 1));

          // Mock profile data
          final profile = UserProfile(
            id: 'mock_user_id',
            name: '<PERSON>',
            email: '<EMAIL>',
            age: 28,
            createdAt: DateTime.now().subtract(const Duration(days: 30)),
            exerciseStats: const ExerciseStats(
              totalExercises: 45,
              memoryMatchingScore: 85,
              wordRecallScore: 78,
              averageScore: 81,
              streak: 7,
            ),
            recentMoods: [
              MoodEntry(
                id: '1',
                moodLevel: 4,
                note: 'Feeling great today!',
                timestamp: DateTime.now().subtract(const Duration(hours: 2)),
                tags: const ['energetic', 'happy'],
                activityBefore: 'Exercise',
              ),
              MoodEntry(
                id: '2',
                moodLevel: 3,
                timestamp: DateTime.now().subtract(const Duration(days: 1)),
                tags: const ['neutral'],
                activityBefore: 'Work',
              ),
              MoodEntry(
                id: '3',
                moodLevel: 5,
                note: 'Excellent mood after exercise',
                timestamp: DateTime.now().subtract(const Duration(days: 2)),
                tags: const ['excited', 'social', 'happy'],
                activityBefore: 'Social activity',
              ),
            ],
          );

          emit(_Loaded(profile));
        }

        if (event is _UpdateRequested) {
          if (state is _Loaded) {
            final currentProfile = (state as _Loaded).profile;
            emit(const _Loading());

            // Simulate API call delay
            await Future.delayed(const Duration(seconds: 1));

            final updatedProfile = currentProfile.copyWith(
              name: event.name,
              age: event.age,
            );

            emit(_Loaded(updatedProfile));
          }
        }

        if (event is _AvatarUpdateRequested) {
          if (state is _Loaded) {
            final currentProfile = (state as _Loaded).profile;
            emit(const _Loading());

            // Simulate API call delay
            await Future.delayed(const Duration(seconds: 1));

            final updatedProfile = currentProfile.copyWith(
              avatarUrl: event.avatarUrl,
            );

            emit(_Loaded(updatedProfile));
          }
        }
      } on Exception catch (e) {
        emit(_Error(e.toString()));
      }
    });
  }
}
