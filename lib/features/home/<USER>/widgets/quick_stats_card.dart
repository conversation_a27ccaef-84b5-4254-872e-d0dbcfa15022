import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../core/theme/app_theme.dart';
import '../../../../shared/presentation/widgets/responsive_layout.dart';
import '../../../profile/presentation/bloc/profile/profile_bloc.dart';
import '../../../profile/domain/entities/user_profile.dart';

class QuickStatsCard extends StatelessWidget {
  const QuickStatsCard({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ProfileBloc, ProfileState>(
      builder: (context, state) {
        return Card(
          child: Padding(
            padding: EdgeInsets.all(AppTheme.spacing20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.analytics,
                      color: AppTheme.primaryColor,
                      size: 20.sp,
                    ),
                    SizedBox(width: AppTheme.spacing8),
                    ResponsiveText(
                      'Quick Stats',
                      style: AppTheme.titleMedium.copyWith(
                        fontWeight: FontWeight.w600,
                        color: AppTheme.textPrimary,
                      ),
                    ),
                  ],
                ),
                SizedBox(height: AppTheme.spacing16),
                state.when(
                  initial: () => _buildLoadingState(),
                  loading: () => _buildLoadingState(),
                  loaded: (profile) => _buildStatsGrid(profile.exerciseStats),
                  error: (message) => _buildErrorState(),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildLoadingState() {
    return SizedBox(
      height: 120.h,
      child: Center(
        child: CircularProgressIndicator(
          color: AppTheme.primaryColor,
          strokeWidth: 2.w,
        ),
      ),
    );
  }

  Widget _buildErrorState() {
    return SizedBox(
      height: 120.h,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              color: AppTheme.textTertiary,
              size: 32.sp,
            ),
            SizedBox(height: AppTheme.spacing8),
            ResponsiveText(
              'Unable to load stats',
              style: AppTheme.bodySmall.copyWith(
                color: AppTheme.textTertiary,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatsGrid(ExerciseStats stats) {
    return ResponsiveLayout(
      mobile: _buildMobileGrid(stats),
      tablet: _buildTabletGrid(stats),
      desktop: _buildDesktopGrid(stats),
    );
  }

  Widget _buildMobileGrid(ExerciseStats stats) {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: _buildStatItem(
                icon: Icons.psychology,
                label: 'Exercises',
                value: '${stats.totalExercises}',
                color: AppTheme.primaryColor,
              ),
            ),
            SizedBox(width: AppTheme.spacing12),
            Expanded(
              child: _buildStatItem(
                icon: Icons.trending_up,
                label: 'Avg Score',
                value: '${stats.averageScore}%',
                color: AppTheme.secondaryColor,
              ),
            ),
          ],
        ),
        SizedBox(height: AppTheme.spacing12),
        Row(
          children: [
            Expanded(
              child: _buildStatItem(
                icon: Icons.memory,
                label: 'Memory',
                value: '${stats.memoryMatchingScore}%',
                color: AppTheme.accentColor,
              ),
            ),
            SizedBox(width: AppTheme.spacing12),
            Expanded(
              child: _buildStatItem(
                icon: Icons.record_voice_over,
                label: 'Verbal',
                value: '${stats.wordRecallScore}%',
                color: AppTheme.warningColor,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildTabletGrid(ExerciseStats stats) {
    return Row(
      children: [
        Expanded(
          child: _buildStatItem(
            icon: Icons.psychology,
            label: 'Total Exercises',
            value: '${stats.totalExercises}',
            color: AppTheme.primaryColor,
            isLarge: true,
          ),
        ),
        SizedBox(width: AppTheme.spacing16),
        Expanded(
          child: _buildStatItem(
            icon: Icons.trending_up,
            label: 'Average Score',
            value: '${stats.averageScore}%',
            color: AppTheme.secondaryColor,
            isLarge: true,
          ),
        ),
        SizedBox(width: AppTheme.spacing16),
        Expanded(
          child: _buildStatItem(
            icon: Icons.memory,
            label: 'Memory Score',
            value: '${stats.memoryMatchingScore}%',
            color: AppTheme.accentColor,
            isLarge: true,
          ),
        ),
        SizedBox(width: AppTheme.spacing16),
        Expanded(
          child: _buildStatItem(
            icon: Icons.record_voice_over,
            label: 'Verbal Score',
            value: '${stats.wordRecallScore}%',
            color: AppTheme.warningColor,
            isLarge: true,
          ),
        ),
      ],
    );
  }

  Widget _buildDesktopGrid(ExerciseStats stats) {
    return Row(
      children: [
        Expanded(
          child: _buildStatItem(
            icon: Icons.psychology,
            label: 'Total Exercises Completed',
            value: '${stats.totalExercises}',
            color: AppTheme.primaryColor,
            isLarge: true,
            showTrend: true,
            trendValue: '+12%',
            isPositiveTrend: true,
          ),
        ),
        SizedBox(width: AppTheme.spacing20),
        Expanded(
          child: _buildStatItem(
            icon: Icons.trending_up,
            label: 'Average Performance',
            value: '${stats.averageScore}%',
            color: AppTheme.secondaryColor,
            isLarge: true,
            showTrend: true,
            trendValue: '+5%',
            isPositiveTrend: true,
          ),
        ),
        SizedBox(width: AppTheme.spacing20),
        Expanded(
          child: _buildStatItem(
            icon: Icons.memory,
            label: 'Memory Exercises',
            value: '${stats.memoryMatchingScore}%',
            color: AppTheme.accentColor,
            isLarge: true,
            showTrend: true,
            trendValue: '+8%',
            isPositiveTrend: true,
          ),
        ),
        SizedBox(width: AppTheme.spacing20),
        Expanded(
          child: _buildStatItem(
            icon: Icons.record_voice_over,
            label: 'Verbal Exercises',
            value: '${stats.wordRecallScore}%',
            color: AppTheme.warningColor,
            isLarge: true,
            showTrend: true,
            trendValue: '+3%',
            isPositiveTrend: true,
          ),
        ),
      ],
    );
  }

  Widget _buildStatItem({
    required IconData icon,
    required String label,
    required String value,
    required Color color,
    bool isLarge = false,
    bool showTrend = false,
    String? trendValue,
    bool isPositiveTrend = true,
  }) {
    return Container(
      padding: EdgeInsets.all(isLarge ? AppTheme.spacing16 : AppTheme.spacing12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
        border: Border.all(
          color: color.withOpacity(0.2),
          width: 1.w,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: EdgeInsets.all(isLarge ? 8.w : 6.w),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(AppTheme.radiusSmall),
                ),
                child: Icon(
                  icon,
                  color: color,
                  size: isLarge ? 20.sp : 16.sp,
                ),
              ),
              if (showTrend && trendValue != null) ...[
                const Spacer(),
                Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: 6.w,
                    vertical: 2.h,
                  ),
                  decoration: BoxDecoration(
                    color: isPositiveTrend 
                        ? AppTheme.successColor.withOpacity(0.1)
                        : AppTheme.errorColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(AppTheme.radiusSmall),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        isPositiveTrend 
                            ? Icons.trending_up 
                            : Icons.trending_down,
                        color: isPositiveTrend 
                            ? AppTheme.successColor
                            : AppTheme.errorColor,
                        size: 12.sp,
                      ),
                      SizedBox(width: 2.w),
                      ResponsiveText(
                        trendValue,
                        style: AppTheme.labelSmall.copyWith(
                          color: isPositiveTrend 
                              ? AppTheme.successColor
                              : AppTheme.errorColor,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ],
          ),
          SizedBox(height: isLarge ? AppTheme.spacing12 : AppTheme.spacing8),
          ResponsiveText(
            value,
            style: (isLarge ? AppTheme.headlineMedium : AppTheme.titleMedium).copyWith(
              fontWeight: FontWeight.w700,
              color: color,
            ),
          ),
          SizedBox(height: 4.h),
          ResponsiveText(
            label,
            style: (isLarge ? AppTheme.bodySmall : AppTheme.labelSmall).copyWith(
              color: AppTheme.textSecondary,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }
}
