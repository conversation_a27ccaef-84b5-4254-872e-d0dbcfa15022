import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';

import '../../../../core/theme/app_theme.dart';
import '../../../../core/router/app_router.dart';
import '../../../../shared/presentation/widgets/responsive_layout.dart';
import '../../../profile/presentation/bloc/profile/profile_bloc.dart';
import '../../../exercises/presentation/bloc/exercises/exercises_bloc.dart';
import '../../../mood/presentation/bloc/mood/mood_bloc.dart';
import '../widgets/home_header.dart';
import '../widgets/quick_stats_card.dart';
import '../widgets/recent_activities_card.dart';
import '../widgets/mood_check_in_card.dart';
import '../widgets/recommended_exercises_card.dart';

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  @override
  void initState() {
    super.initState();
    _loadData();
  }

  void _loadData() {
    context.read<ProfileBloc>().add(const ProfileEvent.loadRequested());
    context.read<ExercisesBloc>().add(const ExercisesEvent.loadRequested());
    context.read<MoodBloc>().add(const MoodEvent.loadRequested());
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: ResponsiveLayout(
        mobile: _buildMobileLayout(),
        tablet: _buildTabletLayout(),
        desktop: _buildDesktopLayout(),
      ),
    );
  }

  Widget _buildMobileLayout() {
    return SafeArea(
      child: RefreshIndicator(
        onRefresh: () async {
          _loadData();
        },
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          padding: EdgeInsets.all(AppTheme.spacing16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const HomeHeader(),
              SizedBox(height: AppTheme.spacing24),
              const QuickStatsCard(),
              SizedBox(height: AppTheme.spacing16),
              const RecommendedExercisesCard(),
              SizedBox(height: AppTheme.spacing16),
              const MoodCheckInCard(),
              SizedBox(height: AppTheme.spacing16),
              const RecentActivitiesCard(),
              SizedBox(height: AppTheme.spacing24),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTabletLayout() {
    return SafeArea(
      child: RefreshIndicator(
        onRefresh: () async {
          _loadData();
        },
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          padding: EdgeInsets.all(AppTheme.spacing24),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const HomeHeader(),
              SizedBox(height: AppTheme.spacing32),
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(
                    flex: 2,
                    child: Column(
                      children: [
                        const QuickStatsCard(),
                        SizedBox(height: AppTheme.spacing16),
                        const RecommendedExercisesCard(),
                      ],
                    ),
                  ),
                  SizedBox(width: AppTheme.spacing16),
                  Expanded(
                    child: Column(
                      children: [
                        const MoodCheckInCard(),
                        SizedBox(height: AppTheme.spacing16),
                        const RecentActivitiesCard(),
                      ],
                    ),
                  ),
                ],
              ),
              SizedBox(height: AppTheme.spacing32),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDesktopLayout() {
    return SafeArea(
      child: RefreshIndicator(
        onRefresh: () async {
          _loadData();
        },
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          child: ResponsiveContainer(
            maxWidth: 1200.w,
            padding: EdgeInsets.all(AppTheme.spacing32),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const HomeHeader(),
                SizedBox(height: AppTheme.spacing40),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(
                      flex: 3,
                      child: Column(
                        children: [
                          const QuickStatsCard(),
                          SizedBox(height: AppTheme.spacing24),
                          const RecommendedExercisesCard(),
                        ],
                      ),
                    ),
                    SizedBox(width: AppTheme.spacing24),
                    Expanded(
                      flex: 2,
                      child: Column(
                        children: [
                          const MoodCheckInCard(),
                          SizedBox(height: AppTheme.spacing24),
                          const RecentActivitiesCard(),
                        ],
                      ),
                    ),
                  ],
                ),
                SizedBox(height: AppTheme.spacing40),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

// Quick action floating button for mobile
class QuickActionButton extends StatelessWidget {
  const QuickActionButton({super.key});

  @override
  Widget build(BuildContext context) {
    return FloatingActionButton.extended(
      onPressed: () {
        _showQuickActionSheet(context);
      },
      backgroundColor: AppTheme.primaryColor,
      foregroundColor: AppTheme.textOnPrimary,
      icon: const Icon(Icons.add),
      label: ResponsiveText(
        'Quick Start',
        style: AppTheme.labelMedium.copyWith(
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  void _showQuickActionSheet(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: AppTheme.surfaceColor,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(AppTheme.radiusLarge),
        ),
      ),
      builder: (context) => Container(
        padding: EdgeInsets.all(AppTheme.spacing24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ResponsiveText(
              'Quick Actions',
              style: AppTheme.titleLarge.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: AppTheme.spacing16),
            _buildQuickActionItem(
              context,
              icon: Icons.psychology,
              title: 'Start Exercise',
              subtitle: 'Begin a cognitive exercise',
              onTap: () {
                Navigator.pop(context);
                context.go(AppRouter.exercises);
              },
            ),
            _buildQuickActionItem(
              context,
              icon: Icons.mood,
              title: 'Log Mood',
              subtitle: 'Track your current mood',
              onTap: () {
                Navigator.pop(context);
                context.go(AppRouter.moodTracking);
              },
            ),
            _buildQuickActionItem(
              context,
              icon: Icons.analytics,
              title: 'View Progress',
              subtitle: 'Check your performance',
              onTap: () {
                Navigator.pop(context);
                context.go(AppRouter.progress);
              },
            ),
            SizedBox(height: AppTheme.spacing16),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActionItem(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return ListTile(
      leading: Container(
        width: 48.w,
        height: 48.h,
        decoration: BoxDecoration(
          color: AppTheme.primaryColor.withOpacity(0.1),
          borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
        ),
        child: Icon(
          icon,
          color: AppTheme.primaryColor,
          size: 24.sp,
        ),
      ),
      title: ResponsiveText(
        title,
        style: AppTheme.titleMedium.copyWith(
          fontWeight: FontWeight.w600,
        ),
      ),
      subtitle: ResponsiveText(
        subtitle,
        style: AppTheme.bodySmall.copyWith(
          color: AppTheme.textSecondary,
        ),
      ),
      onTap: onTap,
      contentPadding: EdgeInsets.zero,
    );
  }
}
