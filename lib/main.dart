import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';

import 'core/theme/app_theme.dart';
import 'core/router/app_router.dart';
import 'core/database/database_helper.dart';
import 'features/auth/presentation/bloc/auth/auth_bloc.dart';
import 'features/profile/presentation/bloc/profile/profile_bloc.dart';
import 'features/exercises/presentation/bloc/exercises/exercises_bloc.dart';
import 'features/mood/presentation/bloc/mood/mood_bloc.dart';

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Load environment variables
  await dotenv.load(fileName: ".env");

  // Initialize Supabase with environment variables
  await Supabase.initialize(
    url: dotenv.env['SUPABASE_URL']!,
    anonKey: dotenv.env['SUPABASE_API_KEY']!,
  );

  // Initialize SQLite database
  final databaseHelper = DatabaseHelper();
  await databaseHelper
      .database; // This will create the database if it doesn't exist
  runApp(const RecallLoopApp());
}

class RecallLoopApp extends StatelessWidget {
  const RecallLoopApp({super.key});

  @override
  Widget build(BuildContext context) {
    return ScreenUtilInit(
      designSize: const Size(375, 812), // iPhone 12 Pro design size
      minTextAdapt: true,
      splitScreenMode: true,
      builder: (context, child) {
        return MultiBlocProvider(
          providers: [
            BlocProvider(create: (context) => AuthBloc()),
            BlocProvider(create: (context) => ProfileBloc()),
            BlocProvider(create: (context) => ExercisesBloc()),
            BlocProvider(create: (context) => MoodBloc()),
          ],
          child: MaterialApp.router(
            title: 'RecallLoop',
            debugShowCheckedModeBanner: false,
            theme: AppTheme.lightTheme,
            darkTheme: AppTheme.darkTheme,
            themeMode: ThemeMode.system,
            routerConfig: AppRouter.router,
          ),
        );
      },
    );
  }
}
